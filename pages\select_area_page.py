"""
Select Area Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for selecting the area of interest.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import numpy as np
import pandas as pd
import logging

# Import common modules
from common.constants import APP_TITLE
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import get_suggested_batch_size

# Import utility functions
from utils import data_utils
from utils.data_utils import get_surfaces, get_well_marker_pairs # Corrected imports
from utils.data_utils import get_nearest_trace_index
from utils.general_utils import parse_polyline_string, find_traces_near_polyline
from utils.data_validation import reset_loading_state
from utils.aoi_validation import (
    validate_aoi_selection,
    process_aoi_selection,
    show_aoi_summary,
    initialize_aoi_session_state,
    show_aoi_boundary_suggestions,
    clear_aoi_boundaries,
    initialize_suggested_aoi_boundaries,
    validate_aoi_size_performance,
    show_aoi_preview
)
from utils.error_handling import (
    handle_errors,
    show_error_with_recovery,
    validate_session_state_prerequisites,
    AOIError,
    ValidationError
)
from common.validation_ui import (
    create_validation_and_loading_section,
    create_proceed_button_with_validation,
    show_selection_summary
)

# Import GPU functions if available
try:
    from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked
    GPU_FUNCTIONS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Could not import GPU functions: {e}")
    GPU_FUNCTIONS_AVAILABLE = False
    # Define dummy function
    def dlogst_spec_descriptor_gpu_2d_chunked(*args, **kwargs):
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")

def render(trace_selection=False):
    """Render the select area page UI."""
    initialize_session_state()

    # Determine if we should show the full UI based on current_step
    show_full_ui = st.session_state.current_step in ["select_mode", "select_traces"]

    if show_full_ui:
        st.header("Step 3: Select Analysis Mode")
        st.sidebar.header("Mode Selection")

        # Check if data is loaded
        if not st.session_state.header_loader:
            st.warning("Please load data first.")
            st.session_state.current_step = "load_data"
            st.rerun()
            return

        # Mode Selection with a unique key
        mode_options = [
            "By well markers",
            "Single inline (all crosslines)",
            "Single crossline (all inlines)",
            "By inline/crossline section (AOI)",
            "By Polyline File Import"
        ]
        # Initialize mode_selector if not already in session state
        if 'mode_selector' not in st.session_state:
            st.session_state.mode_selector = st.session_state.selection_mode or mode_options[0]
        # Determine the index for the selectbox
        mode_index = mode_options.index(st.session_state.mode_selector) if st.session_state.mode_selector in mode_options else 0
        # Create the selectbox without on_change callback
        selected_mode = st.sidebar.selectbox(
            "Select Mode",
            options=mode_options,
            index=mode_index,
            key="mode_selector"
        )
        # Update the selection_mode in session state and reset area_selected if mode changed
        if st.session_state.selection_mode != selected_mode:
            st.session_state.area_selected = False  # Reset area selection when mode changes
            st.session_state.area_selected_mode = None
        st.session_state.selection_mode = selected_mode

        # AOI Mode - Enhanced Implementation
        if st.session_state.selection_mode == "By inline/crossline section (AOI)":
            st.subheader("🎯 AOI (Area of Interest) Selection")

            # Add clear instructions for manual input
            st.info("📝 **Manual AOI Definition**: You have full control to define custom inline and crossline ranges for your Area of Interest. "
                   "Use the input fields below to specify exact boundaries, or choose from suggested presets.")

            # Initialize AOI session state
            initialize_aoi_session_state()

            # Get actual inline/crossline ranges from the loaded SEG-Y file
            if st.session_state.header_loader:
                # Get the full inline/crossline range
                range_dict = st.session_state.header_loader.get_inline_crossline_range()
                actual_inline_min = range_dict['inline_min']
                actual_inline_max = range_dict['inline_max']
                actual_xline_min = range_dict['xline_min']
                actual_xline_max = range_dict['xline_max']

                # Create headers DataFrame for validation
                if not hasattr(st.session_state, 'headers_df') or st.session_state.headers_df is None:
                    st.session_state.headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })

                # AOI Definition Mode Selection
                st.markdown("---")
                st.write("**AOI Definition Mode:**")

                aoi_mode = st.radio(
                    "Choose how to define your AOI:",
                    options=["Use Suggested AOI", "Define Custom AOI"],
                    index=0 if st.session_state.aoi_definition_mode == "suggested" else 1,
                    help="Suggested AOI provides a smart default based on your data. Custom AOI lets you define exact boundaries.",
                    key="aoi_definition_mode_radio"
                )

                # Update session state based on selection
                if aoi_mode == "Use Suggested AOI":
                    st.session_state.aoi_definition_mode = "suggested"
                    # Initialize suggested boundaries if not already done or if switching modes
                    if (st.session_state.aoi_inline_min is None or
                        not st.session_state.aoi_auto_initialized):
                        initialize_suggested_aoi_boundaries(st.session_state.headers_df)
                else:
                    st.session_state.aoi_definition_mode = "manual"
                    # Clear auto-initialization flag when switching to manual
                    st.session_state.aoi_auto_initialized = False
                    # If switching to manual mode and no boundaries are set, provide empty defaults
                    if st.session_state.aoi_inline_min is None:
                        st.session_state.aoi_inline_min = actual_inline_min
                        st.session_state.aoi_inline_max = actual_inline_max
                        st.session_state.aoi_xline_min = actual_xline_min
                        st.session_state.aoi_xline_max = actual_xline_max

                # Define callback function for AOI changes
                def _handle_aoi_change():
                    """Handle AOI boundary changes and trigger re-validation."""
                    # Force re-validation by clearing validation state
                    if 'aoi_validation_result' in st.session_state:
                        del st.session_state.aoi_validation_result
                    # Reset area selection to force re-validation
                    st.session_state.area_selected = False
                    st.session_state.area_selected_mode = None
                    # Reset AOI confirmation states when boundaries change
                    st.session_state.aoi_boundaries_confirmed = False
                    st.session_state.aoi_selection_confirmed = False
                
                # AOI boundary input interface with enhanced validation
                st.markdown("---")
                st.write("**📍 Define AOI Boundaries:**")

                # Show current mode information
                if st.session_state.aoi_definition_mode == "suggested":
                    st.success("🎯 Using Suggested AOI - Values below are automatically calculated but can be manually adjusted")
                else:
                    st.info("✏️ Custom AOI Mode - Define your exact boundaries below")

                st.info(f"📊 **Available data range** - Inline: {actual_inline_min:,}-{actual_inline_max:,}, Crossline: {actual_xline_min:,}-{actual_xline_max:,}")

                # Add helper buttons
                col_help1, col_help2, col_help3 = st.columns(3)
                with col_help1:
                    if st.button("🔄 Reset to Full Range", help="Set AOI to cover the entire survey area"):
                        st.session_state.aoi_inline_min = actual_inline_min
                        st.session_state.aoi_inline_max = actual_inline_max
                        st.session_state.aoi_xline_min = actual_xline_min
                        st.session_state.aoi_xline_max = actual_xline_max
                        st.rerun()

                with col_help2:
                    if st.button("🎯 Reset to Suggested", help="Reset to automatically suggested AOI boundaries"):
                        initialize_suggested_aoi_boundaries(st.session_state.headers_df)
                        st.rerun()

                with col_help3:
                    if st.button("🗑️ Clear All", help="Clear all AOI boundaries for fresh input"):
                        clear_aoi_boundaries()
                        st.rerun()

                # Show boundary suggestions
                show_aoi_boundary_suggestions()

                # Enhanced input fields with better styling
                st.markdown("**🔢 Manual Input Fields:**")
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**📏 Inline Range:**")

                    # Check if boundaries are defined
                    inline_min_value = int(st.session_state.aoi_inline_min) if st.session_state.aoi_inline_min is not None else actual_inline_min
                    inline_max_value = int(st.session_state.aoi_inline_max) if st.session_state.aoi_inline_max is not None else actual_inline_max

                    st.session_state.aoi_inline_min = st.number_input(
                        "Inline Minimum",
                        min_value=int(actual_inline_min),
                        max_value=int(actual_inline_max),
                        value=inline_min_value,
                        step=1,
                        key="aoi_inline_min_input",
                        on_change=_handle_aoi_change,
                        help=f"Enter the minimum inline value for your AOI (Range: {actual_inline_min:,} - {actual_inline_max:,})"
                    )
                    st.session_state.aoi_inline_max = st.number_input(
                        "Inline Maximum",
                        min_value=int(actual_inline_min),
                        max_value=int(actual_inline_max),
                        value=inline_max_value,
                        step=1,
                        key="aoi_inline_max_input",
                        on_change=_handle_aoi_change,
                        help=f"Enter the maximum inline value for your AOI (Range: {actual_inline_min:,} - {actual_inline_max:,})"
                    )

                with col2:
                    st.markdown("**📐 Crossline Range:**")

                    # Check if boundaries are defined
                    xline_min_value = int(st.session_state.aoi_xline_min) if st.session_state.aoi_xline_min is not None else actual_xline_min
                    xline_max_value = int(st.session_state.aoi_xline_max) if st.session_state.aoi_xline_max is not None else actual_xline_max

                    st.session_state.aoi_xline_min = st.number_input(
                        "Crossline Minimum",
                        min_value=int(actual_xline_min),
                        max_value=int(actual_xline_max),
                        value=xline_min_value,
                        step=1,
                        key="aoi_xline_min_input",
                        on_change=_handle_aoi_change,
                        help=f"Enter the minimum crossline value for your AOI (Range: {actual_xline_min:,} - {actual_xline_max:,})"
                    )
                    st.session_state.aoi_xline_max = st.number_input(
                        "Crossline Maximum",
                        min_value=int(actual_xline_min),
                        max_value=int(actual_xline_max),
                        value=xline_max_value,
                        step=1,
                        key="aoi_xline_max_input",
                        on_change=_handle_aoi_change,
                        help=f"Enter the maximum crossline value for your AOI (Range: {actual_xline_min:,} - {actual_xline_max:,})"
                    )
                
                # Enhanced real-time AOI validation and feedback
                st.markdown("---")
                st.write("**🔍 AOI Validation & Statistics:**")

                # Perform real-time validation
                validation_result = validate_aoi_selection()
                if validation_result['valid']:
                    trace_count = validation_result['trace_count']

                    # Check performance implications
                    performance_check = validate_aoi_size_performance(trace_count)

                    # Show validation status with performance warnings
                    if performance_check['warnings']:
                        st.warning(f"⚠️ Valid AOI: {trace_count:,} traces selected - {performance_check['warnings'][0]}")
                        for recommendation in performance_check['recommendations']:
                            st.info(f"💡 {recommendation}")
                    else:
                        st.success(f"✅ Valid AOI: {trace_count:,} traces selected")

                    # Show AOI summary
                    show_aoi_summary()

                    # Show additional AOI statistics with enhanced metrics
                    from utils.aoi_validation import get_aoi_trace_statistics
                    stats = get_aoi_trace_statistics()
                    if 'error' not in stats:
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("Grid Coverage", f"{stats['grid_coverage']:.1%}")
                        with col2:
                            st.metric("Inline Span", stats['inline_range'])
                        with col3:
                            st.metric("Crossline Span", stats['crossline_range'])
                        with col4:
                            # Calculate AOI size as percentage of full survey
                            total_inlines = actual_inline_max - actual_inline_min + 1
                            total_xlines = actual_xline_max - actual_xline_min + 1
                            total_possible = total_inlines * total_xlines
                            aoi_percentage = (trace_count / total_possible) * 100 if total_possible > 0 else 0
                            st.metric("Survey Coverage", f"{aoi_percentage:.1f}%")

                        # Show detailed boundary information
                        st.markdown("**📊 AOI Details:**")
                        detail_col1, detail_col2 = st.columns(2)
                        with detail_col1:
                            st.write(f"• **Inline Range:** {st.session_state.aoi_inline_min:,} to {st.session_state.aoi_inline_max:,}")
                            st.write(f"• **Total Inlines:** {st.session_state.aoi_inline_max - st.session_state.aoi_inline_min + 1:,}")
                        with detail_col2:
                            st.write(f"• **Crossline Range:** {st.session_state.aoi_xline_min:,} to {st.session_state.aoi_xline_max:,}")
                            st.write(f"• **Total Crosslines:** {st.session_state.aoi_xline_max - st.session_state.aoi_xline_min + 1:,}")
                else:
                    st.error(f"❌ Invalid AOI: {validation_result['error']}")
                    st.info("💡 **How to fix:** Adjust the boundary values above to create a valid AOI. Ensure min values are less than max values and within the available data range.")

                # Processing option (Fixed for now)
                st.markdown("---")
                st.info("**Processing Option:** Full AOI")
                st.session_state.aoi_processing_option = "Full AOI"

                # Show AOI preview
                st.markdown("---")
                show_aoi_preview()

                # Store the AOI bounds for reference
                st.session_state.aoi_bounds = {
                    'inline_min': st.session_state.aoi_inline_min,
                    'inline_max': st.session_state.aoi_inline_max,
                    'xline_min': st.session_state.aoi_xline_min,
                    'xline_max': st.session_state.aoi_xline_max
                }

                # Create the header DataFrame for compatibility
                if not hasattr(st.session_state, 'headers_df'):
                    st.session_state.headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })

                # Enhanced AOI Selection Workflow
                st.markdown("---")
                st.write("**🎯 Finalize AOI Selection:**")

                validation_result = validate_aoi_selection()
                if validation_result['valid']:
                    trace_count = validation_result['trace_count']

                    # Check performance implications
                    performance_check = validate_aoi_size_performance(trace_count)

                    # Show confirmation dialog with appropriate styling based on AOI size
                    if performance_check['warnings']:
                        st.warning(f"⚠️ **Ready to proceed with large AOI:** {trace_count:,} traces selected")
                        for warning in performance_check['warnings']:
                            st.warning(f"⚠️ {warning}")
                        for recommendation in performance_check['recommendations']:
                            st.info(f"💡 {recommendation}")

                        # Add confirmation checkbox for large AOIs
                        large_aoi_confirmed = st.checkbox(
                            "I understand this is a large AOI and processing may take longer",
                            value=False,
                            key="large_aoi_confirmation"
                        )

                        # Proceed button - disabled until confirmation for large AOIs
                        proceed_disabled = not large_aoi_confirmed
                        proceed_help = "Confirm the checkbox above to proceed with this large AOI"
                    else:
                        # Normal sized AOI - no warnings needed
                        st.success(f"✅ **Ready to proceed:** {trace_count:,} traces selected")
                        proceed_disabled = False
                        proceed_help = "Click to confirm this AOI and proceed to analysis"

                    # Show final summary before proceeding
                    st.write("**Final AOI Summary:**")
                    show_aoi_summary()

                    # Enhanced proceed button
                    if st.button(
                        "✅ Confirm AOI and Proceed to Analysis",
                        type="primary",
                        use_container_width=True,
                        disabled=proceed_disabled,
                        help=proceed_help
                    ):
                        if process_aoi_selection():
                            # Reset loading state when AOI changes
                            current_aoi_key = f"{st.session_state.aoi_inline_min}_{st.session_state.aoi_inline_max}_{st.session_state.aoi_xline_min}_{st.session_state.aoi_xline_max}"
                            if st.session_state.get('last_aoi_selection_key') != current_aoi_key:
                                reset_loading_state('By inline/crossline section (AOI)')
                                st.session_state.last_aoi_selection_key = current_aoi_key
                                st.session_state.validation_required = True

                            # Mark AOI as confirmed and selected
                            st.session_state.aoi_selection_confirmed = True
                            st.rerun()
                else:
                    st.error(f"❌ **Cannot Proceed:** {validation_result['error']}")
                    st.info("💡 **Action Required:** Adjust the boundary values above to create a valid AOI before proceeding.")
                    if st.session_state.get('aoi_boundaries_confirmed', False):
                        st.session_state.aoi_boundaries_confirmed = False

                # Show final proceed section only after AOI is confirmed
                if st.session_state.get('area_selected') and st.session_state.get('area_selected_mode') == 'aoi' and st.session_state.get('aoi_selection_confirmed', False):
                    st.markdown("---")
                    st.write("**Step 3: Proceed to Analysis**")

                    # Show selection summary
                    show_selection_summary(st)

                    # Add validation and loading section
                    st.markdown("---")
                    create_validation_and_loading_section(st, mode_suffix="_aoi")

                    # Add proceed button with validation
                    st.markdown("---")
                    def on_proceed_aoi():
                        st.session_state.selected_data_for_precompute = {
                            "mode": "aoi",
                            "aoi_bounds": st.session_state.aoi_bounds,
                            "selected_indices": st.session_state.selected_indices
                        }
                        st.session_state.current_step = "analyze_data"

                    if create_proceed_button_with_validation(
                        st,
                        "Proceed to Analysis",
                        "aoi_proceed_button",
                        on_proceed_aoi,
                        mode_suffix="_aoi"
                    ):
                        st.rerun()
            else:
                st.warning("SEG-Y file not properly loaded. Please reload in Step 1.")
        
        # Well Markers Mode - Enhanced Implementation
        elif st.session_state.selection_mode == "By well markers":
            st.subheader("🎯 Well Marker Selection")

            # Validate prerequisites
            if st.session_state.well_df is None or st.session_state.well_df.empty:
                st.error("❌ No well data available. Please upload well data in Step 1.")
                if st.button("← Go to Load Data"):
                    st.session_state.current_step = "load_data"
                    st.rerun()
                return

            # Display HFC percentile value from Step 2
            hfc_val = st.session_state.get('hfc_p95')
            if hfc_val is not None:
                st.info(f"Using HFC percentile value from Step 2: {hfc_val:.3f}")
            else:
                st.warning("HFC percentile value not configured. Please complete Step 2.")

            try:
                # Use the correct function to get available markers
                available_markers = get_surfaces(st.session_state.well_df) # Corrected function call
                st.session_state.selected_well_markers = st.multiselect(
                    "Select Well Markers",
                    options=available_markers,
                    default=available_markers[:min(len(available_markers), 3)]  # Default to first 3 or fewer
                )

                # --- Add well-marker pair dropdown ---
                # Get all well-marker pairs
                well_marker_dict = get_well_marker_pairs(st.session_state.well_df)
                # Filter pairs by selected markers
                filtered_pairs = [pair for pair in well_marker_dict.keys()
                                  if pair.split(" - ")[1] in st.session_state.selected_well_markers]
                if filtered_pairs:
                    # Default to selecting the first pair if none are selected yet, or if the previous single selection is in the list
                    default_selection = []
                    if st.session_state.get('selected_well_marker_pairs'):
                        # If it was a single string before, make it a list
                        current_selection = st.session_state.selected_well_marker_pairs
                        if isinstance(current_selection, str):
                            current_selection = [current_selection]
                        # Keep valid previous selections that are in the filtered_pairs
                        default_selection = [s for s in current_selection if s in filtered_pairs]

                    if not default_selection and filtered_pairs: # If still empty and pairs exist, pick the first one
                        default_selection = [filtered_pairs[0]]

                    st.session_state.selected_well_marker_pairs = st.multiselect(
                        "Select Well-Marker Pair(s) for Analysis", # Changed label to match reference
                        options=filtered_pairs,
                        default=default_selection, # Ensure default is a list
                        key="well_marker_pair_multiselect" # New key might be good
                    )

                    # Plot Mode selection (matching reference implementation)
                    st.session_state.plot_mode_wells = st.radio(
                        "Plot Mode",
                        options=[1, 2],
                        format_func=lambda x: "Individual Plots" if x == 1 else "Comparative Plots",
                        index=0,  # Default to Individual Plots
                        key="plot_mode_wells_radio"
                    )

                    # Map plot_mode_wells to well_analysis_sub_option for compatibility
                    if st.session_state.plot_mode_wells == 1:
                        st.session_state.well_analysis_sub_option = "Plot Individual Wells Analysis"
                    else:
                        st.session_state.well_analysis_sub_option = "Grouping Well Analysis"

                else:
                    st.session_state.selected_well_marker_pairs = None
                    st.info("No well-marker pairs available for the selected markers.")

                # The plot_twt checkbox remains.
                st.session_state.plot_twt = st.checkbox("Plot Two-Way Travel Time (TWT)", value=False)

                # Add a button to load the selected well marker data
                if st.button("Load Data", key="load_well_marker_data"):
                    if st.session_state.selected_well_marker_pairs:
                        # Get well marker pairs dictionary (following original implementation pattern)
                        well_marker_dict = get_well_marker_pairs(st.session_state.well_df)

                        # Get the selected DataFrame indices (following original pattern)
                        selected_indices = [well_marker_dict[pair] for pair in st.session_state.selected_well_marker_pairs if pair in well_marker_dict]
                        selected_traces = []

                        for idx in selected_indices:
                            # Access DataFrame row directly (following original pattern)
                            row = st.session_state.well_df.iloc[idx]

                            # Extract coordinates directly from row (following original pattern)
                            well_x, well_y = row["X"], row["Y"]

                            # Find nearest trace index (following original pattern)
                            trace_idx = get_nearest_trace_index(
                                st.session_state.header_loader,
                                well_x,
                                well_y
                            )
                            if trace_idx is not None:
                                selected_traces.append(trace_idx)

                        if selected_traces:
                            st.session_state.selected_indices = list(set(selected_traces)) # Use unique indices
                            st.session_state.area_selected = True
                            st.session_state.area_selected_mode = 'well_marker'
                            st.session_state.area_selected_details = {
                                'type': 'well_marker',
                                'pairs': st.session_state.selected_well_marker_pairs,
                                'count': len(st.session_state.selected_indices)
                            }
                            reset_loading_state('By well markers')
                            st.session_state.validation_required = True
                            st.rerun()
                        else:
                            st.error("Could not find matching traces for the selected well-marker pairs.")
                    else:
                        st.warning("Please select at least one well-marker pair.")

                # Show validation section if well marker data is selected
                if st.session_state.get('area_selected') and st.session_state.get('area_selected_mode') == 'well_marker':
                    show_selection_summary(st)

                    st.markdown("---")
                    create_validation_and_loading_section(st, mode_suffix="_well_marker")

                    st.markdown("---")
                    def on_proceed_well_marker():
                        st.session_state.selected_data_for_precompute = None
                        st.session_state.precomputation_complete = True
                        st.session_state.current_step = "analyze_data"

                    if create_proceed_button_with_validation(
                        st,
                        "Proceed to Analysis",
                        "well_marker_proceed_button",
                        on_proceed_well_marker,
                        mode_suffix="_well_marker"
                    ):
                        st.rerun()

            except Exception as e:
                st.error(f"❌ Error processing well marker selection: {str(e)}")
                logging.error(f"Well marker selection error: {str(e)}", exc_info=True)

        # Single Inline Mode - Enhanced Implementation
        elif st.session_state.selection_mode == "Single inline (all crosslines)":
            st.subheader("📏 Single Inline Selection")

            # Validate prerequisites
            if not st.session_state.header_loader:
                st.error("❌ SEG-Y headers not loaded. Please load data in Step 1.")
                if st.button("← Go to Load Data"):
                    st.session_state.current_step = "load_data"
                    st.rerun()
                return

            try:
                import numpy as np
                # Get min and max inline numbers
                min_inline = int(np.min(st.session_state.header_loader.inlines))
                max_inline = int(np.max(st.session_state.header_loader.inlines))

                # Default to previously selected inline or min inline
                default_inline = st.session_state.selected_inline if st.session_state.selected_inline else min_inline

                # Create two columns for better organization
                col1, col2 = st.columns([2, 1])

                with col1:
                    # Inline number selection
                    selected_inline = st.number_input(
                        f"Specify an inline number ({min_inline}-{max_inline}):",
                        min_value=min_inline,
                        max_value=max_inline,
                        value=default_inline,
                        step=1,
                        key="inline_number_input"
                    )

                # Update session state for the selected inline
                st.session_state.selected_inline = selected_inline

                # Batch size selection if GPU is available
                if st.session_state.get('GPU_AVAILABLE', False):
                    suggested_batch, free_mb = get_suggested_batch_size()
                    st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")

                    # Initialize batch_size in session state if not present
                    if 'batch_size' not in st.session_state:
                        st.session_state.batch_size = suggested_batch

                    st.session_state.batch_size = st.number_input(
                        "Batch size for GPU processing:",
                        min_value=10,
                        max_value=4000,
                        value=st.session_state.batch_size,
                        step=10,
                        help="Number of traces to process at once. Higher values use more GPU memory."
                    )
                else:
                    st.warning("GPU processing not available. Processing will be slower.")
                    st.session_state.batch_size = None

                # Show trace count preview and confirm button
                inline_mask = st.session_state.header_loader.inlines == st.session_state.selected_inline
                chosen_indices = st.session_state.header_loader.unique_indices[inline_mask]

                if len(chosen_indices) == 0:
                    st.error(f"No traces found for inline {st.session_state.selected_inline}")
                else:
                    st.info(f"Found {len(chosen_indices)} traces for inline {st.session_state.selected_inline}")

                    # Add a button to confirm the selection
                    if st.button("Confirm Selection", key="confirm_inline_selection"):
                        # Set selected indices for validation
                        st.session_state.selected_indices = chosen_indices.tolist()
                        st.session_state.area_selected = True
                        st.session_state.area_selected_mode = 'inline'
                        st.session_state.area_selected_details = {
                            'type': 'single_inline',
                            'inline': st.session_state.selected_inline,
                            'count': len(chosen_indices)
                        }

                        # Reset loading state when selection changes
                        if st.session_state.get('last_selected_inline') != st.session_state.selected_inline:
                            reset_loading_state('Single inline (all crosslines)')
                            st.session_state.last_selected_inline = st.session_state.selected_inline
                            st.session_state.validation_required = True
                        
                        st.rerun()

                # Show validation section if inline is selected
                if st.session_state.get('area_selected') and st.session_state.get('area_selected_mode') == 'inline':
                    # Show selection summary
                    show_selection_summary(st)

                    # Add validation and loading section
                    st.markdown("---")
                    create_validation_and_loading_section(st, mode_suffix="_inline")

                    # Add proceed button with validation
                    st.markdown("---")
                    def on_proceed_inline():
                        st.session_state.selected_data_for_precompute = None
                        st.session_state.precomputation_complete = True
                        st.session_state.current_step = "analyze_data"

                    if create_proceed_button_with_validation(
                        st,
                        "Proceed to Analysis",
                        "inline_proceed_button",
                        on_proceed_inline,
                        mode_suffix="_inline"
                    ):
                        st.rerun()

            except Exception as e:
                st.error(f"❌ Error processing inline selection: {str(e)}")
                logging.error(f"Inline selection error: {str(e)}", exc_info=True)



        # Single Crossline Mode - Enhanced Implementation
        elif st.session_state.selection_mode == "Single crossline (all inlines)":
            st.subheader("📐 Single Crossline Selection")

            # Validate prerequisites
            if not st.session_state.header_loader:
                st.error("❌ SEG-Y headers not loaded. Please load data in Step 1.")
                if st.button("← Go to Load Data"):
                    st.session_state.current_step = "load_data"
                    st.rerun()
                return

            try:
                import numpy as np
                # Get min and max crossline numbers
                min_crossline = int(np.min(st.session_state.header_loader.crosslines))
                max_crossline = int(np.max(st.session_state.header_loader.crosslines))

                # Default to previously selected crossline or min crossline
                default_crossline = st.session_state.selected_crossline if st.session_state.selected_crossline else min_crossline

                # Crossline number selection
                selected_crossline = st.number_input(
                    f"Specify a crossline number ({min_crossline}-{max_crossline}):",
                    min_value=min_crossline,
                    max_value=max_crossline,
                    value=default_crossline,
                    step=1,
                    key="crossline_number_input"
                )

                # Update session state for the selected crossline
                st.session_state.selected_crossline = selected_crossline

                # Batch size selection if GPU is available
                if st.session_state.get('GPU_AVAILABLE', False):
                    suggested_batch, free_mb = get_suggested_batch_size()
                    st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")

                    # Initialize batch_size in session state if not present
                    if 'batch_size' not in st.session_state:
                        st.session_state.batch_size = suggested_batch

                    st.session_state.batch_size = st.number_input(
                        "Batch size for GPU processing:",
                        min_value=10,
                        max_value=4000,
                        value=st.session_state.batch_size,
                        step=10,
                        help="Number of traces to process at once. Higher values use more GPU memory.",
                        key="crossline_batch_size_input"
                    )
                else:
                    st.warning("GPU processing not available. Processing will be slower.")
                    st.session_state.batch_size = None

                # Show trace count preview and confirm button
                crossline_mask = st.session_state.header_loader.crosslines == st.session_state.selected_crossline
                chosen_indices = st.session_state.header_loader.unique_indices[crossline_mask]

                if len(chosen_indices) == 0:
                    st.error(f"No traces found for crossline {st.session_state.selected_crossline}")
                else:
                    st.info(f"Found {len(chosen_indices)} traces for crossline {st.session_state.selected_crossline}")

                    # Add a button to confirm the selection
                    if st.button("Confirm Selection", key="confirm_crossline_selection"):
                        # Set selected indices for validation
                        st.session_state.selected_indices = chosen_indices.tolist()
                        st.session_state.area_selected = True
                        st.session_state.area_selected_mode = 'crossline'
                        st.session_state.area_selected_details = {
                            'type': 'single_crossline',
                            'crossline': st.session_state.selected_crossline,
                            'count': len(chosen_indices)
                        }

                        # Reset loading state when selection changes
                        if st.session_state.get('last_selected_crossline') != st.session_state.selected_crossline:
                            reset_loading_state('Single crossline (all inlines)')
                            st.session_state.last_selected_crossline = st.session_state.selected_crossline
                            st.session_state.validation_required = True
                        
                        st.rerun()

                # Show validation section if crossline is selected
                if st.session_state.get('area_selected') and st.session_state.get('area_selected_mode') == 'crossline':
                    # Show selection summary
                    show_selection_summary(st)

                    # Add validation and loading section
                    st.markdown("---")
                    create_validation_and_loading_section(st, mode_suffix="_crossline")

                    # Add proceed button with validation
                    st.markdown("---")
                    def on_proceed_crossline():
                        st.session_state.selected_data_for_precompute = None
                        st.session_state.precomputation_complete = True
                        st.session_state.current_step = "analyze_data"

                    if create_proceed_button_with_validation(
                        st,
                        "Proceed to Analysis",
                        "crossline_proceed_button",
                        on_proceed_crossline,
                        mode_suffix="_crossline"
                    ):
                        st.rerun()

            except Exception as e:
                st.error(f"❌ Error processing crossline selection: {str(e)}")
                logging.error(f"Crossline selection error: {str(e)}", exc_info=True)



        # Polyline Mode - Enhanced Implementation
        elif st.session_state.selection_mode == "By Polyline File Import":
            st.subheader("📍 Polyline Selection")

            # Polyline File Upload
            st.subheader("Upload Polyline File")
            polyline_file = st.file_uploader("Choose a polyline file", type=["txt", "csv"], key="polyline_file_uploader")
            if polyline_file is not None:
                st.session_state.polyline_file_info = {
                    'name': polyline_file.name,
                    'buffer': polyline_file
                }
                st.success(f"Uploaded {polyline_file.name}")

                # Show tolerance slider only after file is uploaded
                st.session_state.polyline_tolerance = st.slider(
                    "Polyline Tolerance",
                    min_value=0.0,
                    max_value=20.0,  # Maximum value of 20.0
                    value=st.session_state.polyline_tolerance,
                    step=1.0,  # Changed from 0.1 to 1.0
                    key="polyline_tolerance_slider",
                    help="Maximum distance from polyline to include traces (larger values select more traces)"
                )

                # Preview polyline and show trace count estimate
                if st.button("Preview Polyline Selection", key="preview_polyline_button"):
                    with st.spinner("Parsing polyline and finding traces..."):
                        try:
                            # Parse polyline file
                            polyline_content = st.session_state.polyline_file_info['buffer'].getvalue().decode('utf-8')
                            polyline_vertices = parse_polyline_string(polyline_content)
                            st.session_state.polyline_vertices = polyline_vertices

                            # Find traces near polyline
                            tolerance = st.session_state.polyline_tolerance
                            selected_indices = find_traces_near_polyline(
                                st.session_state.header_loader,
                                polyline_vertices,
                                tolerance
                            )

                            if not selected_indices:
                                st.warning(f"No traces found within {tolerance} units of the polyline. Try increasing the tolerance.")
                            else:
                                st.success(f"Preview: Found {len(selected_indices)} traces within {tolerance} units of the polyline.")
                                st.session_state.polyline_preview_count = len(selected_indices)
                                st.session_state.polyline_preview_indices = selected_indices

                        except Exception as e:
                            st.error(f"Error parsing polyline: {e}")
                            logging.error(f"Polyline parsing failed: {e}", exc_info=True)

                # Show preview results if available
                if hasattr(st.session_state, 'polyline_preview_count'):
                    st.info(f"Preview: {st.session_state.polyline_preview_count} traces found with current tolerance setting.")

                    # Set up polyline selection for validation
                    if st.session_state.polyline_preview_indices:
                        st.session_state.selected_indices = st.session_state.polyline_preview_indices
                        st.session_state.area_selected = True
                        st.session_state.area_selected_mode = 'polyline'
                        st.session_state.area_selected_details = {
                            'type': 'polyline',
                            'file': st.session_state.polyline_file_info['name'],
                            'tolerance': st.session_state.polyline_tolerance,
                            'count': len(st.session_state.polyline_preview_indices)
                        }

                        # Reset loading state when selection changes
                        current_polyline_key = f"{st.session_state.polyline_file_info['name']}_{st.session_state.polyline_tolerance}"
                        if st.session_state.get('last_polyline_selection_key') != current_polyline_key:
                            reset_loading_state('By Polyline File Import')
                            st.session_state.last_polyline_selection_key = current_polyline_key
                            st.session_state.validation_required = True

                        # Show selection summary
                        show_selection_summary(st)

                        # Add validation and loading section
                        st.markdown("---")
                        create_validation_and_loading_section(st, mode_suffix="_polyline")

                        # Add proceed button with validation
                        st.markdown("---")
                        def on_proceed_polyline():
                            st.session_state.selected_data_for_precompute = None
                            st.session_state.precomputation_complete = True
                            st.session_state.current_step = "analyze_data"

                        if create_proceed_button_with_validation(
                            st,
                            "Proceed to Analysis",
                            "polyline_proceed_button",
                            on_proceed_polyline,
                            mode_suffix="_polyline"
                        ):
                            st.rerun()

            else:
                st.warning("Please upload a polyline file before proceeding.")

        # Add a separator before the proceed button
        st.markdown("---")

        # Proceed Button with a more specific key and primary styling
        st.markdown("### Complete Step 3 and Continue")

        # Check if display_params_configured is set, if not, set it to True if stats_defaults is available
        if not st.session_state.get('display_params_configured') and st.session_state.get('stats_defaults') is not None:
            logging.info("Setting display_params_configured to True as stats_defaults is available")
            st.session_state.display_params_configured = True

        # Log current state for debugging
        logging.info(f"Before button click - display_params_configured: {st.session_state.get('display_params_configured')}")
        logging.info(f"Before button click - area_selected: {st.session_state.get('area_selected')}")

        if st.button("Next: Analyze Data", key="select_area_next_button", use_container_width=True, type="primary"):
            # Process selection based on mode
            if st.session_state.selection_mode == "By well markers":
                selected_labels = st.session_state.get('selected_well_marker_pairs', []) # Get the labels from the multiselect
                if not selected_labels:
                    st.warning("Please select at least one well-marker pair to continue.")
                    st.stop() # Stop execution to prevent transition

                selected_pairs_data = []
                all_selected_trace_indices = []
                well_df = st.session_state.get('well_df') # Get well_df safely

                if well_df is not None and not well_df.empty:
                    with st.spinner("Processing selected well-marker pairs..."):
                        # Get well marker pairs dictionary (following original implementation pattern)
                        well_marker_dict = get_well_marker_pairs(well_df)

                        # Get the selected DataFrame indices (following original pattern)
                        selected_indices = [well_marker_dict[label] for label in selected_labels if label in well_marker_dict]

                        for idx in selected_indices:
                            try:
                                row = well_df.iloc[idx]
                                pair_data = row.to_dict()
                                well_x, well_y = row["X"], row["Y"]

                                if st.session_state.header_loader:
                                    trace_idx = get_nearest_trace_index(st.session_state.header_loader, well_x, well_y)
                                    if trace_idx is not None:
                                        pair_data['trace_idx'] = trace_idx
                                        selected_pairs_data.append(pair_data)
                                        all_selected_trace_indices.append(trace_idx)
                                    else:
                                        logging.warning(f"Could not find nearest trace for well-marker pair: {row['Well']} - {row['Surface']}")
                                else:
                                    st.error("SEG-Y headers not loaded. Cannot find trace indices.")
                                    st.stop()

                            except Exception as e:
                                logging.error(f"Error processing well-marker pair at index {idx}: {e}")
                                st.error(f"Error processing well-marker pair at index {idx}. See logs for details.")
                                st.stop()

                    st.session_state.selected_well_marker_pairs_data = selected_pairs_data
                    
                    trace_idx_to_well_marker_map = {
                        item['trace_idx']: f"{item['Well']} - {item['Surface']}"
                        for item in selected_pairs_data
                        if 'trace_idx' in item and 'Well' in item and 'Surface' in item
                    }
                    st.session_state.trace_idx_to_well_marker_map = trace_idx_to_well_marker_map
                    logging.info(f"Created trace_idx to well marker map with {len(trace_idx_to_well_marker_map)} entries.")
                    
                    st.session_state.selected_indices = list(set(all_selected_trace_indices))
                    st.session_state.area_selected_details = {'type': 'well_markers', 'count': len(selected_pairs_data), 'labels': selected_labels}
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'well_markers'
                    st.session_state.selected_data_for_precompute = selected_pairs_data

                    st.session_state.precomputation_complete = True
                    logging.info("Setting precomputation_complete flag to True to skip precomputation step")

                    loaded_trace_data = []
                    for pair_data in selected_pairs_data:
                        trace_idx = pair_data.get('trace_idx')
                        if trace_idx is not None:
                            trace_sample = data_utils.load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                            
                            well_name = pair_data.get('Well')
                            surface_name = pair_data.get('Surface')
                            well_marker_name = f"{well_name} - {surface_name}" if well_name and surface_name else None

                            trace_data = {
                                'trace_sample': trace_sample,
                                'trace_idx': trace_idx,
                                'well_marker_name': well_marker_name,
                                'marker_value': pair_data.get('TWT_sec', None) if st.session_state.plot_twt else None
                            }
                            loaded_trace_data.append(trace_data)

                    st.session_state.loaded_trace_data = loaded_trace_data

                    # Calculate HFC and Spectral Decrease percentile values for Option 1 workflow
                    # This is crucial since Option 1 skips the precompute_qc_page.py where these values are normally calculated
                    try:
                        from utils.processing import run_precomputation

                        # Extract trace samples for processing
                        original_traces = [item['trace_sample'] for item in loaded_trace_data]

                        # Use default precomputation parameters for Option 1
                        default_precomputation_params = {
                            "apply_smoothing": True,
                            "smoothing_window": 5,
                            "apply_normalization": True,
                            "normalization_method": "Max Amplitude",
                            "apply_filter": False,
                            "filter_type": None,
                            "filter_params": {}
                        }

                        # Run pre-computation to get descriptors
                        processed_traces = run_precomputation(original_traces, default_precomputation_params)

                        # Calculate HFC and Spectral Decrease percentile values
                        hfc_values = []
                        spec_decrease_values = []
                        for desc in processed_traces:
                            if hasattr(desc, 'get') and desc.get('hfc') is not None:
                                hfc_values.extend(desc['hfc'])
                            if hasattr(desc, 'get') and desc.get('spec_decrease') is not None:
                                spec_decrease_values.extend(desc['spec_decrease'])

                        if hfc_values:
                            # Get the user-configured HFC percentile (default to 95 if not specified)
                            hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
                            hfc_pc = np.percentile(hfc_values, hfc_percentile)
                            # Store percentile cutoff value for normalization
                            st.session_state.plot_settings['hfc_pc'] = float(hfc_pc)
                            # Keep backward compatibility
                            st.session_state.plot_settings['hfc_p95'] = float(hfc_pc)
                            logging.info(f"Option 1: Calculated HFC percentile cutoff (p{hfc_percentile}): {hfc_pc}")

                        if spec_decrease_values:
                            # Get the user-configured Spectral Decrease percentile (default to 95 if not specified)
                            spec_decrease_percentile = st.session_state.plot_settings.get('spec_decrease_percentile', 95.0)
                            spec_decrease_pc = np.percentile(spec_decrease_values, spec_decrease_percentile)
                            # Store percentile cutoff value for normalization
                            st.session_state.plot_settings['spec_decrease_pc'] = float(spec_decrease_pc)
                            # Keep backward compatibility
                            st.session_state.plot_settings['spec_decrease_p95'] = float(spec_decrease_pc)
                            logging.info(f"Option 1: Calculated Spectral Decrease percentile cutoff (p{spec_decrease_percentile}): {spec_decrease_pc}")

                    except Exception as e:
                        logging.warning(f"Could not calculate percentile values for Option 1: {e}")
                        # Continue anyway - the robust helper functions will handle missing values

                    st.success(f"{len(selected_pairs_data)} well-marker pair(s) selected. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                    st.session_state.current_step = "analyze_data" # Transition directly to analyze_data
                    st.rerun()

                else:
                    st.warning("Well data not loaded or empty. Cannot process well-marker selections.")
                    st.stop() # Stop execution

            elif st.session_state.selection_mode == "Single inline (all crosslines)":
                # Logic for Single Inline
                if st.session_state.header_loader and st.session_state.get('selected_inline') is not None:
                    # Only proceed if area_selected is already True (set by the Proceed button)
                    if st.session_state.get('area_selected', False):
                        st.session_state.current_step = "analyze_data"
                        st.rerun()
                    else:
                        # Find and display traces but don't proceed yet
                        headers_df = pd.DataFrame({
                            'inline': st.session_state.header_loader.inlines,
                            'crossline': st.session_state.header_loader.crosslines,
                            'trace_idx': st.session_state.header_loader.unique_indices
                        })
                        selected_inline_df = headers_df[headers_df['inline'] == st.session_state.selected_inline]
                        trace_count = len(selected_inline_df)

                        if trace_count > 0:
                            st.warning(f"Please click 'Proceed to Analysis' above to confirm selection of {trace_count} traces for inline {st.session_state.selected_inline}.")
                        else:
                            st.error(f"No traces found for inline {st.session_state.selected_inline}. Please select a different inline.")
                        st.stop()
                else:
                    st.warning("Please select an inline number.")
                    st.stop()

            elif st.session_state.selection_mode == "Single crossline (all inlines)":
                # Logic for Single Crossline
                if st.session_state.header_loader and st.session_state.get('selected_crossline') is not None:
                    # Only proceed if area_selected is already True (set by the Proceed button)
                    if st.session_state.get('area_selected', False):
                        st.session_state.current_step = "analyze_data"
                        st.rerun()
                    else:
                        # Find and display traces but don't proceed yet
                        headers_df = pd.DataFrame({
                            'inline': st.session_state.header_loader.inlines,
                            'crossline': st.session_state.header_loader.crosslines,
                            'trace_idx': st.session_state.header_loader.unique_indices
                        })
                        selected_crossline_df = headers_df[headers_df['crossline'] == st.session_state.selected_crossline]
                        trace_count = len(selected_crossline_df)

                        if trace_count > 0:
                            st.warning(f"Please click 'Proceed to Analysis' above to confirm selection of {trace_count} traces for crossline {st.session_state.selected_crossline}.")
                        else:
                            st.error(f"No traces found for crossline {st.session_state.selected_crossline}. Please select a different crossline.")
                        st.stop()
                else:
                    st.warning("Please select a crossline number.")
                    st.stop()

            elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
                # Logic for AOI - need to get all trace indices within the selected AOI bounds
                if st.session_state.header_loader and st.session_state.get('aoi_bounds') is not None:
                     headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })
                     aoi_bounds = st.session_state.aoi_bounds
                     aoi_df = headers_df[
                        (headers_df['inline'] >= aoi_bounds['inline_min']) &
                        (headers_df['inline'] <= aoi_bounds['inline_max']) &
                        (headers_df['crossline'] >= aoi_bounds['xline_min']) &
                        (headers_df['crossline'] <= aoi_bounds['xline_max'])
                    ]
                     st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
                     st.session_state.area_selected_details = {'type': 'aoi', 'bounds': aoi_bounds, 'count': len(st.session_state.selected_indices)}
                     st.session_state.area_selected = True
                     st.session_state.area_selected_mode = 'aoi'
                     st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                     # Set precomputation_complete to True to skip that step
                     st.session_state.precomputation_complete = True

                     st.success(f"Selected AOI. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                     st.session_state.current_step = "analyze_data"
                     st.rerun()
                else:
                    st.warning("Please define the AOI bounds.")
                    st.stop()

            elif st.session_state.selection_mode == "By Polyline File Import":
                # Logic for Polyline - process polyline file and find traces
                if st.session_state.header_loader and st.session_state.get('polyline_file_info') is not None and st.session_state.get('polyline_tolerance') is not None:
                    # Parse polyline file and find traces
                    with st.spinner("Processing polyline selection..."):
                        try:
                            # Parse polyline file
                            polyline_content = st.session_state.polyline_file_info['buffer'].getvalue().decode('utf-8')
                            polyline_vertices = parse_polyline_string(polyline_content)
                            st.session_state.polyline_vertices = polyline_vertices

                            # Find traces near polyline
                            tolerance = st.session_state.polyline_tolerance
                            selected_indices = find_traces_near_polyline(
                                st.session_state.header_loader,
                                polyline_vertices,
                                tolerance
                            )

                            if not selected_indices:
                                st.warning(f"No traces found within {tolerance} units of the polyline. Try increasing the tolerance.")
                                st.stop()
                            else:
                                # Store selected indices and mark area as selected
                                st.session_state.selected_indices = selected_indices
                                st.session_state.area_selected = True
                                st.session_state.area_selected_mode = 'polyline'
                                st.session_state.area_selected_details = {
                                    'type': 'polyline',
                                    'file': st.session_state.polyline_file_info['name'],
                                    'tolerance': st.session_state.polyline_tolerance,
                                    'count': len(selected_indices)
                                }

                                # Load trace data for selected indices
                                loaded_trace_data = []
                                for idx in selected_indices:
                                    trace_sample = data_utils.load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                                    if trace_sample is not None:
                                        loaded_trace_data.append({
                                            'trace_sample': trace_sample,
                                            'trace_idx': idx
                                        })

                                # Store loaded trace data
                                st.session_state.loaded_trace_data = loaded_trace_data

                                # Calculate HFC and Spectral Decrease percentile values for polyline mode
                                if loaded_trace_data:
                                    try:
                                        from utils.processing import run_precomputation

                                        # Extract trace samples for processing
                                        original_traces = [item['trace_sample'] for item in loaded_trace_data]

                                        # Use default precomputation parameters
                                        default_precomputation_params = {
                                            "apply_smoothing": True,
                                            "smoothing_window": 5,
                                            "apply_normalization": True,
                                            "normalization_method": "Max Amplitude",
                                            "apply_filter": False,
                                            "filter_type": None,
                                            "filter_params": {}
                                        }

                                        # Run pre-computation to get descriptors
                                        processed_traces = run_precomputation(original_traces, default_precomputation_params)

                                        # Calculate HFC and Spectral Decrease percentile values
                                        hfc_values = []
                                        spec_decrease_values = []
                                        for desc in processed_traces:
                                            if hasattr(desc, 'get') and desc.get('hfc') is not None:
                                                hfc_values.extend(desc['hfc'])
                                            if hasattr(desc, 'get') and desc.get('spec_decrease') is not None:
                                                spec_decrease_values.extend(desc['spec_decrease'])

                                        if hfc_values:
                                            hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
                                            hfc_pc = np.percentile(hfc_values, hfc_percentile)
                                            st.session_state.plot_settings['hfc_pc'] = float(hfc_pc)
                                            st.session_state.plot_settings['hfc_p95'] = float(hfc_pc)  # Backward compatibility
                                            logging.info(f"Polyline mode: Calculated HFC percentile cutoff (p{hfc_percentile}): {hfc_pc}")

                                        if spec_decrease_values:
                                            spec_decrease_percentile = st.session_state.plot_settings.get('spec_decrease_percentile', 95.0)
                                            spec_decrease_pc = np.percentile(spec_decrease_values, spec_decrease_percentile)
                                            st.session_state.plot_settings['spec_decrease_pc'] = float(spec_decrease_pc)
                                            st.session_state.plot_settings['spec_decrease_p95'] = float(spec_decrease_pc)  # Backward compatibility
                                            logging.info(f"Polyline mode: Calculated Spectral Decrease percentile cutoff (p{spec_decrease_percentile}): {spec_decrease_pc}")

                                    except Exception as e:
                                        logging.warning(f"Could not calculate percentile values for polyline mode: {e}")
                                        # Continue anyway - the robust helper functions will handle missing values

                                st.session_state.selected_data_for_precompute = None

                                # Set precomputation_complete to True to skip that step
                                st.session_state.precomputation_complete = True

                                st.success(f"Found {len(selected_indices)} traces near the polyline. Loaded {len(loaded_trace_data)} traces. Proceeding to Analyze Data.")
                                st.session_state.current_step = "analyze_data"
                                st.rerun()

                        except Exception as e:
                            st.error(f"Error processing polyline: {e}")
                            logging.error(f"Polyline processing failed: {e}", exc_info=True)
                            st.stop()
                else:
                    st.warning("Please upload a polyline file and set tolerance.")
                    st.stop()

            else:
                st.warning("Unknown selection mode. Please select a valid mode.")
                st.stop()

        # Add a "Start New Analysis" button to the sidebar
        st.sidebar.markdown("---")
        if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
            reset_state()
            st.success("Starting new analysis. All temporary data has been cleared.")
            st.rerun()
        return
